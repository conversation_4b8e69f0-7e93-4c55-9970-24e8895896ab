#!/usr/bin/env python3
"""
Тестовый скрипт для проверки новой единой функции форматирования статистики по микротемам
"""

import asyncio
import sys
import os

# Добавляем путь к корневой папке проекта
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database import init_database
from common.statistics import (
    format_microtopic_stats,
    get_student_microtopics_detailed,
    get_student_strong_weak_summary
)


async def test_microtopic_formatting():
    """Тестирование новых функций форматирования"""
    
    # Инициализируем базу данных
    await init_database()
    
    print("🧪 Тестирование единой функции форматирования статистики по микротемам\n")
    
    # Тестовые данные
    student_id = 1  # Предполагаем, что студент с ID 1 существует
    subject_id = 1  # Предполагаем, что предмет с ID 1 существует
    
    print("=" * 60)
    print("📊 ТЕСТ 1: Детальная статистика (format_type='detailed')")
    print("=" * 60)
    
    try:
        detailed_data = await format_microtopic_stats(student_id, subject_id, "detailed")
        print(f"Результат: {detailed_data}")
        print(f"\nТекст:\n{detailed_data['text']}")
        print(f"\nЕсть данные: {detailed_data['has_data']}")
        if detailed_data['has_data']:
            print(f"Сильные темы: {detailed_data.get('strong_topics', [])}")
            print(f"Слабые темы: {detailed_data.get('weak_topics', [])}")
    except Exception as e:
        print(f"❌ Ошибка: {e}")
    
    print("\n" + "=" * 60)
    print("📊 ТЕСТ 2: Сводка (format_type='summary')")
    print("=" * 60)
    
    try:
        summary_data = await format_microtopic_stats(student_id, subject_id, "summary")
        print(f"Результат: {summary_data}")
        print(f"\nТекст:\n{summary_data['text']}")
        print(f"\nЕсть данные: {summary_data['has_data']}")
        if summary_data['has_data']:
            print(f"Сильные темы: {summary_data.get('strong_topics', [])}")
            print(f"Слабые темы: {summary_data.get('weak_topics', [])}")
    except Exception as e:
        print(f"❌ Ошибка: {e}")
    
    print("\n" + "=" * 60)
    print("📊 ТЕСТ 3: Функция для кнопки 'Детальная статистика'")
    print("=" * 60)
    
    try:
        detailed_text = await get_student_microtopics_detailed(student_id, subject_id)
        print(f"Результат:\n{detailed_text}")
    except Exception as e:
        print(f"❌ Ошибка: {e}")
    
    print("\n" + "=" * 60)
    print("📊 ТЕСТ 4: Функция для кнопки 'Сильные и слабые темы'")
    print("=" * 60)
    
    try:
        summary_text = await get_student_strong_weak_summary(student_id, subject_id)
        print(f"Результат:\n{summary_text}")
    except Exception as e:
        print(f"❌ Ошибка: {e}")
    
    print("\n" + "=" * 60)
    print("✅ ТЕСТИРОВАНИЕ ЗАВЕРШЕНО")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(test_microtopic_formatting())
