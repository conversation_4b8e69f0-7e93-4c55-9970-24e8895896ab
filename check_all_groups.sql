-- Проверка ВСЕХ групп и их статистики

-- 1. Все группы и их студенты с процентами выполнения
SELECT 
    g.name as group_name,
    s.name as subject_name,
    u.name as student_name,
    -- Всего ДЗ по предмету группы
    (SELECT COUNT(*) FROM homeworks h WHERE h.subject_id = s.id) as total_homeworks_in_subject,
    -- Уникальных ДЗ выполнено студентом по предмету группы
    COUNT(DISTINCT CASE 
        WHEN h.subject_id = s.id THEN hr.homework_id 
        ELSE NULL 
    END) as unique_completed_in_subject,
    -- Процент выполнения
    ROUND(
        COUNT(DISTINCT CASE WHEN h.subject_id = s.id THEN hr.homework_id ELSE NULL END) * 100.0 / 
        NULLIF((SELECT COUNT(*) FROM homeworks h2 WHERE h2.subject_id = s.id), 0),
        1
    ) as completion_percentage
FROM groups g
JOIN subjects s ON g.subject_id = s.id
JOIN students st ON g.id = st.group_id
JOIN users u ON st.user_id = u.id
LEFT JOIN homework_results hr ON st.id = hr.student_id
LEFT JOIN homeworks h ON hr.homework_id = h.id
GROUP BY g.id, g.name, s.id, s.name, st.id, u.name
ORDER BY g.name, u.name;

-- 2. Средний процент по каждой группе
SELECT 
    g.name as group_name,
    s.name as subject_name,
    COUNT(DISTINCT st.id) as students_count,
    (SELECT COUNT(*) FROM homeworks h WHERE h.subject_id = s.id) as total_homeworks_available,
    ROUND(AVG(
        COUNT(DISTINCT CASE WHEN h.subject_id = s.id THEN hr.homework_id ELSE NULL END) * 100.0 / 
        NULLIF((SELECT COUNT(*) FROM homeworks h2 WHERE h2.subject_id = s.id), 0)
    ), 1) as avg_completion_percentage
FROM groups g
JOIN subjects s ON g.subject_id = s.id
JOIN students st ON g.id = st.group_id
LEFT JOIN homework_results hr ON st.id = hr.student_id
LEFT JOIN homeworks h ON hr.homework_id = h.id
GROUP BY g.id, g.name, s.id, s.name
ORDER BY g.name;
