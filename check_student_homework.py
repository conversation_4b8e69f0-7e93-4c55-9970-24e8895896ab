#!/usr/bin/env python3
"""
Проверка ДЗ студента
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database
from sqlalchemy import text
from database.database import get_db_session


async def check_student_homework():
    """Проверяем ДЗ студента Муханбетжан Олжас"""
    print("🔍 Проверяем ДЗ студента...")
    
    await init_database()
    
    async with get_db_session() as session:
        # Найдем студента
        student_result = await session.execute(
            text("""
                SELECT s.id, u.name, g.name as group_name, subj.name as subject_name
                FROM students s
                JOIN users u ON s.user_id = u.id
                JOIN groups g ON s.group_id = g.id
                LEFT JOIN subjects subj ON g.subject_id = subj.id
                WHERE u.name = 'Муханбетжан Олжас'
            """)
        )
        student = student_result.fetchone()
        
        if not student:
            print("❌ Студент не найден")
            return
            
        print(f"👤 Студент: {student.name}")
        print(f"📚 Группа: {student.group_name}")
        print(f"📖 Предмет группы: {student.subject_name}")
        print(f"🆔 ID студента: {student.id}")
        
        # Сначала проверим, какие таблицы есть в базе
        tables_result = await session.execute(
            text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                ORDER BY table_name
            """)
        )

        print(f"\n📋 Таблицы в базе данных:")
        for row in tables_result.fetchall():
            print(f"   • {row.table_name}")

        # Получаем все ДЗ, которые выполнял студент
        homework_results = await session.execute(
            text("""
                SELECT
                    hr.id,
                    hr.homework_id,
                    hr.points_earned,
                    hr.completed_at
                FROM homework_results hr
                WHERE hr.student_id = :student_id
                ORDER BY hr.completed_at
            """),
            {"student_id": student.id}
        )
        
        print(f"\n📋 Все выполненные ДЗ:")
        unique_homework = set()

        homework_list = homework_results.fetchall()
        for row in homework_list:
            unique_homework.add(row.homework_id)
            print(f"   • ДЗ ID {row.homework_id} - {row.points_earned} баллов - {row.completed_at}")

        print(f"\n📊 Статистика:")
        print(f"   • Всего попыток ДЗ: {len(homework_list)}")
        print(f"   • Уникальных ДЗ: {len(unique_homework)}")

        # Проверим, есть ли таблица month_tests (возможно, ДЗ хранятся там)
        month_tests_result = await session.execute(
            text("""
                SELECT COUNT(*) as count
                FROM month_tests
            """)
        )
        month_tests_count = month_tests_result.fetchone()
        print(f"\n📚 Всего записей в month_tests: {month_tests_count.count if month_tests_count else 0}")

        # Проверим связь homework_results с month_tests
        if len(unique_homework) > 0:
            homework_ids = list(unique_homework)
            month_tests_check = await session.execute(
                text("""
                    SELECT id, title, subject_id
                    FROM month_tests
                    WHERE id = ANY(:homework_ids)
                """),
                {"homework_ids": homework_ids}
            )

            print(f"\n📚 Информация о выполненных ДЗ:")
            for row in month_tests_check.fetchall():
                print(f"   • ID {row.id}: {row.title} (subject_id: {row.subject_id})")


if __name__ == "__main__":
    asyncio.run(check_student_homework())
