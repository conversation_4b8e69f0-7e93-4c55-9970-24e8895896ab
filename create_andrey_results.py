import asyncio
import sys
import os
sys.path.append(os.getcwd())

async def create_andrey_excellent_results():
    """Создать отличные результаты для Андрея Климова"""
    from database import (
        init_database, 
        StudentRepository, 
        HomeworkRepository, 
        QuestionRepository,
        AnswerOptionRepository,
        HomeworkResultRepository,
        QuestionResultRepository
    )
    
    print('🔧 Инициализация базы данных...')
    await init_database()
    
    print('👤 Поиск Андрея Климова...')
    andrey = await StudentRepository.get_by_telegram_id(955518340)
    
    if not andrey:
        print('❌ Андрей Климов не найден')
        return
    
    print(f'✅ Найден: {andrey.user.name} в группе {andrey.group.name} ({andrey.group.subject.name})')
    
    # Получаем все ДЗ по Python
    all_homeworks = await HomeworkRepository.get_all()
    python_homeworks = [hw for hw in all_homeworks if hw.subject_id == andrey.group.subject.id]
    
    print(f'📚 Найдено {len(python_homeworks)} ДЗ по Python')
    
    question_repo = QuestionRepository()
    created_results = 0
    total_points = 0
    
    for homework in python_homeworks:
        print(f'\n🎯 Создаем отличный результат для ДЗ "{homework.name}"...')
        
        # Получаем вопросы для этого ДЗ
        homework_questions = await question_repo.get_by_homework(homework.id)
        if not homework_questions:
            print(f'   ⚠️ Нет вопросов для ДЗ "{homework.name}"')
            continue
        
        total_questions = len(homework_questions)
        correct_answers = total_questions  # 100% результат
        points_earned = total_questions * 3  # Максимальные баллы
        
        # Создаем результат ДЗ
        homework_result = await HomeworkResultRepository.create(
            student_id=andrey.id,
            homework_id=homework.id,
            total_questions=total_questions,
            correct_answers=correct_answers,
            points_earned=points_earned,
            is_first_attempt=True
        )
        
        # Создаем результаты для каждого вопроса
        question_results_data = []
        
        for question in homework_questions:
            # Получаем варианты ответов для вопроса
            answer_options = await AnswerOptionRepository.get_by_question(question.id)
            if not answer_options:
                continue
            
            # Выбираем правильный ответ
            correct_answer = next((opt for opt in answer_options if opt.is_correct), None)
            
            # Быстрое время ответа (как у опытного разработчика)
            import random
            time_spent = random.randint(5, 15)  # 5-15 секунд
            
            question_results_data.append({
                'question_id': question.id,
                'selected_answer_id': correct_answer.id if correct_answer else None,
                'is_correct': True,
                'time_spent': time_spent,
                'microtopic_number': question.microtopic_number
            })
        
        # Создаем результаты вопросов
        if question_results_data:
            await QuestionResultRepository.create_multiple(
                homework_result.id,
                question_results_data
            )
        
        print(f'   ✅ ДЗ "{homework.name}": {correct_answers}/{total_questions} (100%) - {points_earned} баллов')
        created_results += 1
        total_points += points_earned
    
    # Обновляем баллы и уровень студента
    print(f'\n🔄 Обновление статистики...')
    await StudentRepository.update_points_and_level(andrey.id)
    
    # Получаем обновленные данные
    updated_andrey = await StudentRepository.get_by_id(andrey.id)
    if updated_andrey:
        print(f'🎉 РЕЗУЛЬТАТ:')
        print(f'   👤 Студент: {updated_andrey.user.name}')
        print(f'   📚 Предмет: {updated_andrey.group.subject.name}')
        print(f'   📊 Выполнено ДЗ: {created_results}')
        print(f'   💎 Баллы: {updated_andrey.points}')
        print(f'   🏆 Уровень: {updated_andrey.level}')
        
        # Показываем статистику по микротемам
        print(f'\n📈 Статистика по микротемам:')
        microtopic_stats = await StudentRepository.get_microtopic_understanding(
            andrey.id, 
            andrey.group.subject.id
        )
        
        if microtopic_stats:
            from database import MicrotopicRepository
            microtopics = await MicrotopicRepository.get_by_subject(andrey.group.subject.id)
            microtopic_names = {mt.number: mt.name for mt in microtopics}
            
            for number, stats in microtopic_stats.items():
                name = microtopic_names.get(number, f"Микротема {number}")
                percentage = stats['percentage']
                status = "✅" if percentage >= 80 else "❌" if percentage <= 40 else "⚠️"
                print(f'   • {name} — {percentage:.0f}% {status}')
        else:
            print('   ⚠️ Нет данных по микротемам')

if __name__ == "__main__":
    asyncio.run(create_andrey_excellent_results())
