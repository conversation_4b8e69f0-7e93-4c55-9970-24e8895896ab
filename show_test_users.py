import asyncio
import sys
import os
sys.path.append(os.getcwd())

async def show_test_users():
    """Показать существующих студентов для тестирования"""
    from database import init_database, StudentRepository
    
    print('🔧 Инициализация базы данных...')
    await init_database()
    
    print('👥 Доступные студенты для тестирования:')
    students = await StudentRepository.get_all()
    
    for i, student in enumerate(students[:10], 1):  # Показываем первых 10
        group_info = f"{student.group.name} ({student.group.subject.name})" if student.group else "Без группы"
        print(f'{i:2d}. {student.user.name}')
        print(f'    Telegram ID: {student.user.telegram_id}')
        print(f'    Группа: {group_info}')
        print(f'    Баллы: {student.points}, Уровень: {student.level}')
        print()
    
    print('💡 Для тестирования можете использовать любой из этих Telegram ID')
    print('   Просто измените свой Telegram ID в настройках Telegram Desktop на один из указанных')
    print('   Или используйте эмулятор/второй аккаунт с нужным ID')

if __name__ == "__main__":
    asyncio.run(show_test_users())
