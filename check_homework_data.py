#!/usr/bin/env python3
"""
Проверка данных ДЗ в базе
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database
from sqlalchemy import text
from database.database import get_db_session


async def check_homework_data():
    """Проверяем данные ДЗ в базе"""
    print("🔍 Проверяем данные ДЗ в базе...")
    
    await init_database()
    
    async with get_db_session() as session:
        # Проверяем таблицу homeworks
        print("\n📚 Таблица homeworks:")
        homeworks_result = await session.execute(
            text("""
                SELECT h.id, h.name, s.name as subject_name
                FROM homeworks h
                JOIN subjects s ON h.subject_id = s.id
                ORDER BY s.name, h.id
            """)
        )
        
        homeworks_by_subject = {}
        for row in homeworks_result.fetchall():
            subject = row.subject_name
            if subject not in homeworks_by_subject:
                homeworks_by_subject[subject] = []
            homeworks_by_subject[subject].append(f"ID {row.id}: {row.name}")
        
        for subject, hw_list in homeworks_by_subject.items():
            print(f"   {subject}: {len(hw_list)} ДЗ")
            for hw in hw_list:
                print(f"     • {hw}")
        
        # Проверяем таблицу month_tests
        print("\n📚 Таблица month_tests:")
        month_tests_result = await session.execute(
            text("""
                SELECT mt.id, mt.name, s.name as subject_name
                FROM month_tests mt
                LEFT JOIN subjects s ON mt.subject_id = s.id
                ORDER BY s.name, mt.id
            """)
        )
        
        month_tests_by_subject = {}
        for row in month_tests_result.fetchall():
            subject = row.subject_name or "Без предмета"
            if subject not in month_tests_by_subject:
                month_tests_by_subject[subject] = []
            month_tests_by_subject[subject].append(f"ID {row.id}: {row.name}")
        
        for subject, mt_list in month_tests_by_subject.items():
            print(f"   {subject}: {len(mt_list)} тестов")
            for mt in mt_list:
                print(f"     • {mt}")
        
        # Проверяем homework_results - какие ДЗ выполняли студенты
        print("\n📊 Результаты ДЗ (homework_results):")
        results_result = await session.execute(
            text("""
                SELECT 
                    u.name as student_name,
                    g.name as group_name,
                    subj.name as group_subject,
                    hr.homework_id,
                    hr.points_earned,
                    COUNT(*) as attempts
                FROM homework_results hr
                JOIN students s ON hr.student_id = s.id
                JOIN users u ON s.user_id = u.id
                JOIN groups g ON s.group_id = g.id
                LEFT JOIN subjects subj ON g.subject_id = subj.id
                GROUP BY u.name, g.name, subj.name, hr.homework_id, hr.points_earned
                ORDER BY u.name, hr.homework_id
            """)
        )
        
        for row in results_result.fetchall():
            print(f"   {row.student_name} ({row.group_name}, {row.group_subject}): ДЗ ID {row.homework_id} - {row.points_earned} баллов ({row.attempts} попыток)")
        
        # Проверяем, к какой таблице относятся homework_id в homework_results
        print("\n🔗 Связь homework_results с таблицами:")
        
        # Получаем все уникальные homework_id из results
        homework_ids_result = await session.execute(
            text("""
                SELECT DISTINCT homework_id
                FROM homework_results
                ORDER BY homework_id
            """)
        )
        
        homework_ids = [row.homework_id for row in homework_ids_result.fetchall()]
        print(f"   Уникальные homework_id в results: {homework_ids}")
        
        # Проверяем, есть ли они в homeworks
        if homework_ids:
            homeworks_check = await session.execute(
                text("""
                    SELECT id, name, subject_id
                    FROM homeworks
                    WHERE id = ANY(:homework_ids)
                """),
                {"homework_ids": homework_ids}
            )
            
            print(f"   Найдено в таблице homeworks:")
            for row in homeworks_check.fetchall():
                print(f"     • ID {row.id}: {row.name} (subject_id: {row.subject_id})")
            
            # Проверяем, есть ли они в month_tests
            month_tests_check = await session.execute(
                text("""
                    SELECT id, name, subject_id
                    FROM month_tests
                    WHERE id = ANY(:homework_ids)
                """),
                {"homework_ids": homework_ids}
            )
            
            print(f"   Найдено в таблице month_tests:")
            for row in month_tests_check.fetchall():
                print(f"     • ID {row.id}: {row.name} (subject_id: {row.subject_id})")


if __name__ == "__main__":
    asyncio.run(check_homework_data())
