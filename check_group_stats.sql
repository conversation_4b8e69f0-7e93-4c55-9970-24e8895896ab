-- Проверка статистики по группам для диагностики проблемы

-- 1. Количество ДЗ по каждому предмету
SELECT 
    s.name as subject_name,
    COUNT(h.id) as total_homeworks
FROM subjects s
LEFT JOIN homeworks h ON s.id = h.subject_id
GROUP BY s.id, s.name
ORDER BY s.name;

-- 2. Информация о группах и их предметах
SELECT 
    g.id as group_id,
    g.name as group_name,
    s.name as subject_name,
    COUNT(st.id) as students_count
FROM groups g
LEFT JOIN subjects s ON g.subject_id = s.id
LEFT JOIN students st ON g.id = st.group_id
GROUP BY g.id, g.name, s.name
ORDER BY g.name;

-- 3. Статистика выполнения ДЗ по группам
SELECT 
    g.name as group_name,
    s.name as subject_name,
    u.name as student_name,
    COUNT(DISTINCT hr.homework_id) as unique_homeworks_completed,
    COUNT(hr.id) as total_attempts,
    SUM(hr.points_earned) as total_points
FROM groups g
JOIN subjects s ON g.subject_id = s.id
JOIN students st ON g.id = st.group_id
JOIN users u ON st.user_id = u.id
LEFT JOIN homework_results hr ON st.id = hr.student_id
LEFT JOIN homeworks h ON hr.homework_id = h.id AND h.subject_id = s.id
GROUP BY g.id, g.name, s.name, st.id, u.name
ORDER BY g.name, u.name;

-- 4. Детальная статистика по каждой группе
SELECT 
    g.name as group_name,
    s.name as subject_name,
    COUNT(DISTINCT st.id) as total_students,
    COUNT(DISTINCT h.id) as total_homeworks_available,
    ROUND(AVG(student_stats.unique_completed), 2) as avg_unique_completed,
    ROUND(AVG(student_stats.completion_percentage), 1) as avg_completion_percentage
FROM groups g
JOIN subjects s ON g.subject_id = s.id
JOIN students st ON g.id = st.group_id
LEFT JOIN homeworks h ON s.id = h.subject_id
LEFT JOIN (
    SELECT 
        st2.id as student_id,
        st2.group_id,
        COUNT(DISTINCT hr2.homework_id) as unique_completed,
        CASE 
            WHEN COUNT(DISTINCT h2.id) > 0 
            THEN (COUNT(DISTINCT hr2.homework_id) * 100.0 / COUNT(DISTINCT h2.id))
            ELSE 0 
        END as completion_percentage
    FROM students st2
    JOIN groups g2 ON st2.group_id = g2.id
    LEFT JOIN homework_results hr2 ON st2.id = hr2.student_id
    LEFT JOIN homeworks h2 ON hr2.homework_id = h2.id AND h2.subject_id = g2.subject_id
    LEFT JOIN homeworks h2_all ON g2.subject_id = h2_all.subject_id
    GROUP BY st2.id, st2.group_id
) student_stats ON st.id = student_stats.student_id
GROUP BY g.id, g.name, s.name
ORDER BY g.name;

-- 5. Проверка конкретных групп (МАТ-1, ХИМ-1, PY-1)
SELECT 
    'Детальная проверка групп МАТ-1, ХИМ-1, PY-1' as info;

SELECT 
    g.name as group_name,
    s.name as subject_name,
    u.name as student_name,
    -- Всего ДЗ по предмету группы
    (SELECT COUNT(*) FROM homeworks h WHERE h.subject_id = s.id) as total_homeworks_in_subject,
    -- Уникальных ДЗ выполнено студентом по предмету группы
    COUNT(DISTINCT CASE 
        WHEN h.subject_id = s.id THEN hr.homework_id 
        ELSE NULL 
    END) as unique_completed_in_subject,
    -- Процент выполнения
    ROUND(
        COUNT(DISTINCT CASE WHEN h.subject_id = s.id THEN hr.homework_id ELSE NULL END) * 100.0 / 
        NULLIF((SELECT COUNT(*) FROM homeworks h2 WHERE h2.subject_id = s.id), 0),
        1
    ) as completion_percentage
FROM groups g
JOIN subjects s ON g.subject_id = s.id
JOIN students st ON g.id = st.group_id
JOIN users u ON st.user_id = u.id
LEFT JOIN homework_results hr ON st.id = hr.student_id
LEFT JOIN homeworks h ON hr.homework_id = h.id
WHERE g.name IN ('МАТ-1', 'ХИМ-1', 'PY-1')
GROUP BY g.id, g.name, s.id, s.name, st.id, u.name
ORDER BY g.name, u.name;
