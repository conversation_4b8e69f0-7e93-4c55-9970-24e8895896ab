import asyncio
import sys
import os
sys.path.append(os.getcwd())

async def test_progress_menu():
    """Тестирование меню прогресса студента"""
    from database import init_database, StudentRepository
    from student.keyboards.progress import get_subjects_progress_kb
    from common.statistics import format_student_topics_stats_real
    
    print('🔧 Инициализация базы данных...')
    await init_database()
    
    print('🧪 Тестирование клавиатуры предметов...')
    try:
        keyboard = await get_subjects_progress_kb()
        print(f'✅ Клавиатура создана успешно, кнопок: {len(keyboard.inline_keyboard)}')
        for row in keyboard.inline_keyboard:
            for button in row:
                print(f'  - {button.text} -> {button.callback_data}')
    except Exception as e:
        print(f'❌ Ошибка при создании клавиатуры: {e}')
    
    print('\n📊 Тестирование статистики студента...')
    # Берем первого студента для теста
    students = await StudentRepository.get_all()
    if students:
        test_student = students[0]
        print(f'Тестируем студента: {test_student.user.name} (ID: {test_student.id})')
        
        if test_student.group and test_student.group.subject:
            subject_id = test_student.group.subject.id
            print(f'Предмет группы: {test_student.group.subject.name} (ID: {subject_id})')
            
            try:
                # Тестируем функцию статистики
                stats_text = await format_student_topics_stats_real(
                    test_student.id,
                    subject_id,
                    "detailed"
                )
                print(f'✅ Статистика получена:\n{stats_text}')
            except Exception as e:
                print(f'❌ Ошибка при получении статистики: {e}')
        else:
            print('⚠️ У студента нет группы или предмета')
    else:
        print('❌ Студенты не найдены')
    
    print('\n🔍 Тестирование метода get_by_telegram_id...')
    if students:
        test_student = students[0]
        telegram_id = test_student.user.telegram_id
        print(f'Ищем студента по Telegram ID: {telegram_id}')
        
        try:
            found_student = await StudentRepository.get_by_telegram_id(telegram_id)
            if found_student:
                print(f'✅ Студент найден: {found_student.user.name}')
            else:
                print('❌ Студент не найден')
        except Exception as e:
            print(f'❌ Ошибка при поиске студента: {e}')

if __name__ == "__main__":
    asyncio.run(test_progress_menu())
