# 📊 Профилирование производительности Telegram бота

Этот документ описывает инструменты и методы профилирования производительности бота при одновременной работе 50+ учеников.

## 🚀 Быстрый старт

### 1. Установка инструментов
```bash
python scripts/setup_profiling.py
```

### 2. Запуск мониторинга в реальном времени
```bash
python scripts/performance_monitor.py
```

### 3. Нагрузочное тестирование
```bash
python scripts/load_test_homework.py
```

## 🛠️ Инструменты профилирования

### 1. **py-spy** (Рекомендуется)
- **Преимущества**: Работает без изменения кода, минимальное влияние на производительность
- **Использование**: 
  ```bash
  ./scripts/profile_with_py_spy.sh
  ```
- **Результат**: SVG flame graph в `profiling_results/`

### 2. **cProfile + snakeviz**
- **Преимущества**: Детальная статистика функций, встроен в Python
- **Использование**:
  ```bash
  ./scripts/profile_with_cprofile.sh
  ```
- **Анализ**:
  ```bash
  python scripts/analyze_profiling.py
  ```

### 3. **Memory Profiler**
- **Преимущества**: Отслеживание утечек памяти
- **Использование**:
  ```bash
  ./scripts/monitor_memory.sh
  ```

### 4. **Встроенный мониторинг**
- **Преимущества**: Работает в продакшене, сохраняет метрики в Redis
- **Доступ**: `https://edubot.schoolpro.kz/stats`

## 📈 Сценарии тестирования

### Сценарий 1: Базовое профилирование
```bash
# 1. Запускаем бота
python main.py

# 2. В другом терминале запускаем профилирование
./scripts/profile_with_py_spy.sh

# 3. Генерируем нагрузку
python scripts/load_test_homework.py
```

### Сценарий 2: Мониторинг в реальном времени
```bash
# 1. Запускаем мониторинг
python scripts/performance_monitor.py

# 2. В другом терминале запускаем нагрузочный тест
python scripts/load_test_homework.py
```

### Сценарий 3: Анализ памяти
```bash
# 1. Запускаем мониторинг памяти
./scripts/monitor_memory.sh

# 2. Генерируем нагрузку и наблюдаем за потреблением памяти
```

## 🔍 Ключевые метрики

### Производительность запросов
- **Время ответа**: < 1 секунды (норма), > 2 секунд (критично)
- **Пропускная способность**: > 10 запросов/сек
- **Одновременные запросы**: до 50 без деградации

### Ресурсы системы
- **CPU**: < 70% (норма), > 90% (критично)
- **Память**: < 80% (норма), > 95% (критично)
- **База данных**: < 500ms на запрос (норма)

### Redis производительность
- **Время ответа**: < 10ms
- **Соединения**: мониторинг активных подключений
- **Память**: отслеживание роста кэша

## 🚨 Критические точки

### 1. Обработчики домашних заданий
- **Проблема**: Синхронные операции с БД
- **Решение**: Асинхронные запросы, пулы соединений

### 2. Middleware роли
- **Проблема**: Частые запросы к БД для определения роли
- **Решение**: Redis кэширование (уже реализовано)

### 3. FSM состояния
- **Проблема**: Большое количество состояний в памяти
- **Решение**: Redis storage (уже реализовано)

### 4. Клавиатуры
- **Проблема**: Генерация клавиатур с запросами к БД
- **Решение**: Кэширование клавиатур в Redis

## 📊 Анализ результатов

### Flame Graph (py-spy)
- **Красные блоки**: Горячие функции (много времени)
- **Широкие блоки**: Часто вызываемые функции
- **Глубокие стеки**: Сложные вызовы

### cProfile статистика
- **cumtime**: Общее время включая подфункции
- **tottime**: Время только этой функции
- **ncalls**: Количество вызовов

### Memory Profile
- **RSS**: Физическая память
- **VMS**: Виртуальная память
- **Рост**: Потенциальные утечки

## 🔧 Оптимизации

### 1. База данных
```python
# Используйте connection pooling
engine = create_async_engine(
    DATABASE_URL, 
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True
)
```

### 2. Redis кэширование
```python
# Кэшируйте часто используемые данные
@cache_result(ttl=300)
async def get_user_role(user_id: int):
    # Запрос к БД
    pass
```

### 3. Асинхронность
```python
# Используйте gather для параллельных операций
results = await asyncio.gather(
    get_courses(),
    get_subjects(),
    get_lessons()
)
```

## 📝 Логирование производительности

### Автоматическое логирование
- Медленные запросы (> 1 сек) логируются автоматически
- Метрики сохраняются в Redis
- Системная статистика собирается каждые 30 сек

### Ручное логирование
```python
import time
from middlewares.performance_middleware import db_monitor

start_time = time.time()
# Ваш код
execution_time = time.time() - start_time
await db_monitor.log_query("SELECT * FROM users", execution_time)
```

## 🎯 Целевые показатели

### Для 50 одновременных пользователей:
- **Время ответа**: < 1.5 сек (95 перцентиль)
- **Пропускная способность**: > 30 запросов/сек
- **Использование памяти**: < 512MB
- **CPU**: < 60%
- **Успешность**: > 99%

### Для нагрузочных тестов:
- **Время прохождения теста**: < 5 минут на 50 студентов
- **Ошибки**: < 1%
- **Деградация производительности**: < 20%

## 🔄 Непрерывный мониторинг

### Production мониторинг
1. **Grafana + Prometheus**: Для визуализации метрик
2. **Sentry**: Для отслеживания ошибок
3. **New Relic**: Для APM мониторинга

### Алерты
- CPU > 80% в течение 5 минут
- Память > 90% в течение 2 минут
- Время ответа > 3 сек для 10+ запросов
- Ошибки > 5% в течение минуты

## 📞 Поддержка

При возникновении проблем с производительностью:

1. Проверьте логи: `logs/bot_YYYY-MM-DD.log`
2. Запустите мониторинг: `python scripts/performance_monitor.py`
3. Проведите профилирование: `./scripts/profile_with_py_spy.sh`
4. Проанализируйте результаты: `python scripts/analyze_profiling.py`

## 🚀 Дальнейшие улучшения

1. **Кэширование клавиатур** в Redis
2. **Пул соединений** для PostgreSQL
3. **Сжатие данных** в Redis
4. **Lazy loading** для больших списков
5. **Pagination** для результатов запросов
