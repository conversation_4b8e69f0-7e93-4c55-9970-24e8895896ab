import asyncio
import sys
import os
sys.path.append(os.getcwd())

async def test_progress_functions():
    """Тестирование исправленных функций прогресса"""
    from database import init_database, StudentRepository
    from common.statistics import get_student_microtopics_detailed, get_student_strong_weak_summary
    
    print('🔧 Инициализация базы данных...')
    await init_database()
    
    print('👤 Поиск Андрея Климова...')
    andrey = await StudentRepository.get_by_telegram_id(955518340)
    
    if andrey:
        print(f'✅ Найден: {andrey.user.name}')
        print(f'   Группа: {andrey.group.name}')
        print(f'   Предмет: {andrey.group.subject.name} (ID: {andrey.group.subject.id})')
        
        print('\n📈 Тестирование детальной статистики...')
        detailed_stats = await get_student_microtopics_detailed(andrey.id, andrey.group.subject.id)
        print(detailed_stats)
        
        print('\n🟢🔴 Тестирование сводки сильных/слабых тем...')
        summary_stats = await get_student_strong_weak_summary(andrey.id, andrey.group.subject.id)
        print(summary_stats)
        
    else:
        print('❌ Андрей Климов не найден')

if __name__ == "__main__":
    asyncio.run(test_progress_functions())
