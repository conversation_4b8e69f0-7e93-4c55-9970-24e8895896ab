"""
Временное отключение проверки студента для тестирования интерфейса
"""

# Добавьте этот код в начало обработчиков progress.py для тестирования:

TEST_MODE = True  # Включить тестовый режим
TEST_STUDENT_ID = 6  # ID любого студента из базы для тестирования

# В обработчиках замените:
# student = await StudentRepository.get_by_telegram_id(callback.from_user.id)
# 
# На:
# if TEST_MODE:
#     student = await StudentRepository.get_by_id(TEST_STUDENT_ID)
# else:
#     student = await StudentRepository.get_by_telegram_id(callback.from_user.id)

print("Этот файл содержит инструкции для временного отключения проверки студента")
print("Скопируйте код выше в student/handlers/progress.py для тестирования")
