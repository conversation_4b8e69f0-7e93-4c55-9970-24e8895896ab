import asyncio
import sys
import os
sys.path.append(os.getcwd())

async def test_student_progress():
    from database import init_database, StudentRepository, SubjectRepository
    
    print('🔧 Инициализация базы данных...')
    await init_database()
    
    print('📚 Проверка предметов в базе данных...')
    subjects = await SubjectRepository.get_all()
    print(f'Найдено предметов: {len(subjects)}')
    for subject in subjects:
        print(f'  - {subject.name} (ID: {subject.id})')
    
    print('👥 Проверка студентов в базе данных...')
    students = await StudentRepository.get_all()
    print(f'Найдено студентов: {len(students)}')
    for student in students:
        group_name = student.group.name if student.group else "None"
        print(f'  - {student.user.name} (Telegram ID: {student.user.telegram_id}, Group: {group_name})')
    
    if students:
        print('📊 Тестирование статистики первого студента...')
        first_student = students[0]
        stats = await StudentRepository.get_general_stats(first_student.id)
        print(f'Статистика студента {first_student.user.name}: {stats}')
        
        if first_student.group and first_student.group.subject:
            microtopic_stats = await StudentRepository.get_microtopic_understanding(
                first_student.id, 
                first_student.group.subject.id
            )
            print(f'Статистика по микротемам: {microtopic_stats}')

if __name__ == "__main__":
    asyncio.run(test_student_progress())
