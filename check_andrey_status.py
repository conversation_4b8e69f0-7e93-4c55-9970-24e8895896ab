import asyncio
import sys
import os
sys.path.append(os.getcwd())

async def check_andrey_status():
    """Проверить статус Андрея Климова в системе"""
    from database import init_database, StudentRepository, SubjectRepository, HomeworkRepository
    
    print('🔧 Инициализация базы данных...')
    await init_database()
    
    print('👤 Поиск Андрея Климова...')
    andrey = await StudentRepository.get_by_telegram_id(955518340)
    
    if andrey:
        print(f'✅ Найден: {andrey.user.name}')
        print(f'   Роль: {andrey.user.role}')
        print(f'   Группа: {andrey.group.name if andrey.group else "Нет группы"}')
        print(f'   Предмет: {andrey.group.subject.name if andrey.group and andrey.group.subject else "Нет предмета"}')
        print(f'   Баллы: {andrey.points}')
        print(f'   Уровень: {andrey.level}')
        
        # Проверим доступные предметы с ДЗ
        print(f'\n📖 Доступные предметы с ДЗ:')
        all_subjects = await SubjectRepository.get_all()
        all_homeworks = await HomeworkRepository.get_all()

        for subject in all_subjects:
            subject_homeworks = [hw for hw in all_homeworks if hw.subject_id == subject.id]
            if subject_homeworks:
                print(f'  - {subject.name}: {len(subject_homeworks)} ДЗ')
                for hw in subject_homeworks:
                    print(f'    • {hw.name}')

        # Переместим Андрея в группу Python для получения результатов
        print(f'\n🔄 Перемещение в группу Python...')
        from database import GroupRepository
        groups = await GroupRepository.get_all()
        python_group = next((g for g in groups if g.name == "PY-1"), None)

        if python_group:
            success = await StudentRepository.update(andrey.id, group_id=python_group.id)
            if success:
                print(f'✅ Андрей перемещен в группу {python_group.name} (Python)')
            else:
                print('❌ Не удалось переместить в группу Python')
        else:
            print('❌ Группа PY-1 не найдена')
    else:
        print('❌ Андрей Климов не найден в системе')

if __name__ == "__main__":
    asyncio.run(check_andrey_status())
