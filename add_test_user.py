import asyncio
import sys
import os
sys.path.append(os.getcwd())

async def add_test_user():
    """Добавить тестового пользователя для проверки"""
    from database import (
        init_database, 
        UserRepository, 
        StudentRepository, 
        GroupRepository,
        SubjectRepository
    )
    
    print('🔧 Инициализация базы данных...')
    await init_database()
    
    # Ваш Telegram ID (замените на свой)
    your_telegram_id = 123456789  # ЗАМЕНИТЕ НА СВОЙ РЕАЛЬНЫЙ TELEGRAM ID
    your_name = "Тестовый Студент"  # ЗАМЕНИТЕ НА СВОЕ ИМЯ
    
    print(f'👤 Добавление пользователя с Telegram ID: {your_telegram_id}')
    
    try:
        # Проверяем, существует ли уже пользователь
        existing_user = await UserRepository.get_by_telegram_id(your_telegram_id)
        if existing_user:
            print(f'⚠️ Пользователь уже существует: {existing_user.name} (роль: {existing_user.role})')
            user = existing_user
        else:
            # Создаем пользователя
            user = await UserRepository.create(
                telegram_id=your_telegram_id,
                name=your_name,
                role='student'
            )
            print(f'✅ Пользователь создан: {user.name} (ID: {user.id})')
        
        # Проверяем, есть ли профиль студента
        existing_student = await StudentRepository.get_by_telegram_id(your_telegram_id)
        if existing_student:
            print(f'⚠️ Профиль студента уже существует: {existing_student.user.name}')
            if existing_student.group:
                print(f'   Группа: {existing_student.group.name}')
                print(f'   Предмет: {existing_student.group.subject.name}')
        else:
            # Получаем первую доступную группу для назначения
            groups = await GroupRepository.get_all()
            if groups:
                first_group = groups[0]
                print(f'📚 Назначаем в группу: {first_group.name} (предмет: {first_group.subject.name})')
                
                # Создаем профиль студента
                student = await StudentRepository.create(
                    user_id=user.id,
                    group_id=first_group.id,
                    tariff='standard'
                )
                print(f'✅ Профиль студента создан: {student.user.name}')
                print(f'   Группа: {student.group.name}')
                print(f'   Предмет: {student.group.subject.name}')
                print(f'   Баллы: {student.points}')
                print(f'   Уровень: {student.level}')
            else:
                print('❌ Нет доступных групп для назначения')
        
        print(f'\n🎯 Теперь вы можете протестировать бота с Telegram ID: {your_telegram_id}')
        
    except Exception as e:
        print(f'❌ Ошибка: {e}')

if __name__ == "__main__":
    print("⚠️ ВНИМАНИЕ: Отредактируйте файл и укажите свой реальный Telegram ID!")
    print("Найдите строку 'your_telegram_id = 123456789' и замените на свой ID")
    print("Также замените 'your_name' на свое имя")
    print()
    
    # Раскомментируйте следующую строку после редактирования:
    # asyncio.run(add_test_user())
