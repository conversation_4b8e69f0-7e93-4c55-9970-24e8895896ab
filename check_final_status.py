import asyncio
import sys
import os
sys.path.append(os.getcwd())

async def check_final_status():
    """Проверить финальный статус Андрея"""
    from database import init_database, StudentRepository
    from common.statistics import get_student_microtopics_detailed, get_student_strong_weak_summary
    
    print('🔧 Инициализация базы данных...')
    await init_database()
    
    print('👤 Проверка финального статуса Андрея Климова...')
    andrey = await StudentRepository.get_by_telegram_id(955518340)
    
    if andrey:
        print(f'✅ {andrey.user.name}')
        print(f'   📚 Предмет: {andrey.group.subject.name}')
        print(f'   💎 Баллы: {andrey.points}')
        print(f'   🏆 Уровень: {andrey.level}')
        
        print('\n📈 Детальная статистика:')
        detailed = await get_student_microtopics_detailed(andrey.id, andrey.group.subject.id)
        print(detailed)
        
        print('\n🟢🔴 Сводка:')
        summary = await get_student_strong_weak_summary(andrey.id, andrey.group.subject.id)
        print(summary)
        
    else:
        print('❌ Не найден')

if __name__ == "__main__":
    asyncio.run(check_final_status())
